# YOLOv5 🚀 by Ultralytics, AGPL-3.0 license

name: Close stale issues
on:
  schedule:
    - cron: '0 0 * * *'  # Runs at 00:00 UTC every day

jobs:
  stale:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@v8
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          stale-issue-message: |
            👋 Hello, this issue has been automatically marked as stale because it has not had recent activity. Please note it will be closed if no further activity occurs.

            Access additional [YOLOv5](https://ultralytics.com/yolov5) 🚀 resources:
            - **Wiki** – https://github.com/ultralytics/yolov5/wiki
            - **Tutorials** – https://github.com/ultralytics/yolov5#tutorials
            - **Docs** – https://docs.ultralytics.com

            Access additional [Ultralytics](https://ultralytics.com) ⚡ resources:
            - **Ultralytics HUB** – https://ultralytics.com/hub
            - **Vision API** – https://ultralytics.com/yolov5
            - **About Us** – https://ultralytics.com/about
            - **Join Our Team** – https://ultralytics.com/work
            - **Contact Us** – https://ultralytics.com/contact

            Feel free to inform us of any other **issues** you discover or **feature requests** that come to mind in the future. Pull Requests (PRs) are also always welcomed!

            Thank you for your contributions to YOLOv5 🚀 and Vision AI ⭐!

          stale-pr-message: 'This pull request has been automatically marked as stale because it has not had recent activity. It will be closed if no further activity occurs. Thank you for your contributions YOLOv5 🚀 and Vision AI ⭐.'
          days-before-issue-stale: 30
          days-before-issue-close: 10
          days-before-pr-stale: 90
          days-before-pr-close: 30
          exempt-issue-labels: 'documentation,tutorial,TODO'
          operations-per-run: 300  # The maximum number of operations per run, used to control rate limiting.
