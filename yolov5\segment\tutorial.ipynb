{"cells": [{"cell_type": "markdown", "metadata": {"id": "t6MPjfT5NrKQ"}, "source": ["<div align=\"center\">\n", "\n", "  <a href=\"https://ultralytics.com/yolov5\" target=\"_blank\">\n", "    <img width=\"1024\", src=\"https://raw.githubusercontent.com/ultralytics/assets/main/yolov5/v70/splash.png\"></a>\n", "\n", "\n", "<br>\n", "  <a href=\"https://bit.ly/yolov5-paperspace-notebook\"><img src=\"https://assets.paperspace.io/img/gradient-badge.svg\" alt=\"Run on Gradient\"></a>\n", "  <a href=\"https://colab.research.google.com/github/ultralytics/yolov5/blob/master/segment/tutorial.ipynb\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"></a>\n", "  <a href=\"https://www.kaggle.com/ultralytics/yolov5\"><img src=\"https://kaggle.com/static/images/open-in-kaggle.svg\" alt=\"Open In Kaggle\"></a>\n", "<br>\n", "\n", "This <a href=\"https://github.com/ultralytics/yolov5\">YOLOv5</a> 🚀 notebook by <a href=\"https://ultralytics.com\">Ultralytics</a> presents simple train, validate and predict examples to help start your AI adventure.<br>See <a href=\"https://github.com/ultralytics/yolov5/issues/new/choose\">GitHub</a> for community support or <a href=\"https://ultralytics.com/contact\">contact us</a> for professional support.\n", "\n", "</div>"]}, {"cell_type": "markdown", "metadata": {"id": "7mGmQbAO5pQb"}, "source": ["# Setup\n", "\n", "Clone GitHub [repository](https://github.com/ultralytics/yolov5), install [dependencies](https://github.com/ultralytics/yolov5/blob/master/requirements.txt) and check PyTorch and GPU."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "wbvMlHd_QwMG", "outputId": "171b23f0-71b9-4cbf-b666-6fa2ecef70c8"}, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["YOLOv5 🚀 v7.0-2-gc9d47ae Python-3.7.15 torch-1.12.1+cu113 CUDA:0 (Tesla T4, 15110MiB)\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Setup complete ✅ (2 CPUs, 12.7 GB RAM, 22.6/78.2 GB disk)\n"]}], "source": ["!git clone https://github.com/ultralytics/yolov5  # clone\n", "%cd yolov5\n", "%pip install -qr requirements.txt  # install\n", "\n", "import torch\n", "import utils\n", "display = utils.notebook_init()  # checks"]}, {"cell_type": "markdown", "metadata": {"id": "4JnkELT0cIJg"}, "source": ["# 1. Predict\n", "\n", "`segment/predict.py` runs YOLOv5 instance segmentation inference on a variety of sources, downloading models automatically from the [latest YOLOv5 release](https://github.com/ultralytics/yolov5/releases), and saving results to `runs/predict`. Example inference sources are:\n", "\n", "```shell\n", "python segment/predict.py --source 0  # webcam\n", "                             img.jpg  # image \n", "                             vid.mp4  # video\n", "                             screen  # screenshot\n", "                             path/  # directory\n", "                             'path/*.jpg'  # glob\n", "                             'https://youtu.be/Zgi9g1ksQHc'  # YouTube\n", "                             'rtsp://example.com/media.mp4'  # RTSP, RTMP, HTTP stream\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "zR9ZbuQCH7FX", "outputId": "3f67f1c7-f15e-4fa5-d251-967c3b77eaad"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[34m\u001b[1msegment/predict: \u001b[0mweights=['yolov5s-seg.pt'], source=data/images, data=data/coco128.yaml, imgsz=[640, 640], conf_thres=0.25, iou_thres=0.45, max_det=1000, device=, view_img=False, save_txt=False, save_conf=False, save_crop=False, nosave=False, classes=None, agnostic_nms=False, augment=False, visualize=False, update=False, project=runs/predict-seg, name=exp, exist_ok=False, line_thickness=3, hide_labels=False, hide_conf=False, half=False, dnn=False, vid_stride=1, retina_masks=False\n", "YOLOv5 🚀 v7.0-2-gc9d47ae Python-3.7.15 torch-1.12.1+cu113 CUDA:0 (Tesla T4, 15110MiB)\n", "\n", "Downloading https://github.com/ultralytics/yolov5/releases/download/v7.0/yolov5s-seg.pt to yolov5s-seg.pt...\n", "100% 14.9M/14.9M [00:01<00:00, 12.0MB/s]\n", "\n", "Fusing layers... \n", "YOLOv5s-seg summary: 224 layers, 7611485 parameters, 0 gradients, 26.4 GFLOPs\n", "image 1/2 /content/yolov5/data/images/bus.jpg: 640x480 4 persons, 1 bus, 18.2ms\n", "image 2/2 /content/yolov5/data/images/zidane.jpg: 384x640 2 persons, 1 tie, 13.4ms\n", "Speed: 0.5ms pre-process, 15.8ms inference, 18.5ms NMS per image at shape (1, 3, 640, 640)\n", "Results saved to \u001b[1mruns/predict-seg/exp\u001b[0m\n"]}], "source": ["!python segment/predict.py --weights yolov5s-seg.pt --img 640 --conf 0.25 --source data/images\n", "#display.Image(filename='runs/predict-seg/exp/zidane.jpg', width=600)"]}, {"cell_type": "markdown", "metadata": {"id": "hkAzDWJ7cWTr"}, "source": ["&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\n", "<img align=\"left\" src=\"https://user-images.githubusercontent.com/26833433/199030123-08c72f8d-6871-4116-8ed3-c373642cf28e.jpg\" width=\"600\">"]}, {"cell_type": "markdown", "metadata": {"id": "0eq1SMWl6Sfn"}, "source": ["# 2. <PERSON><PERSON><PERSON>\n", "Validate a model's accuracy on the [COCO](https://cocodataset.org/#home) dataset's `val` or `test` splits. Models are downloaded automatically from the [latest YOLOv5 release](https://github.com/ultralytics/yolov5/releases). To show results by class use the `--verbose` flag."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "WQPtK1QYVaD_", "outputId": "9d751d8c-bee8-4339-cf30-9854ca530449"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Downloading https://github.com/ultralytics/yolov5/releases/download/v1.0/coco2017labels-segments.zip  ...\n", "Downloading http://images.cocodataset.org/zips/val2017.zip ...\n", "######################################################################## 100.0%\n", "######################################################################## 100.0%\n"]}], "source": ["# Download COCO val\n", "!bash data/scripts/get_coco.sh --val --segments  # download (780M - 5000 images)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "X58w8JLpMnjH", "outputId": "a140d67a-02da-479e-9ddb-7d54bf9e407a"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[34m\u001b[1msegment/val: \u001b[0mdata=/content/yolov5/data/coco.yaml, weights=['yolov5s-seg.pt'], batch_size=32, imgsz=640, conf_thres=0.001, iou_thres=0.6, max_det=300, task=val, device=, workers=8, single_cls=False, augment=False, verbose=False, save_txt=False, save_hybrid=False, save_conf=False, save_json=False, project=runs/val-seg, name=exp, exist_ok=False, half=True, dnn=False\n", "YOLOv5 🚀 v7.0-2-gc9d47ae Python-3.7.15 torch-1.12.1+cu113 CUDA:0 (Tesla T4, 15110MiB)\n", "\n", "Fusing layers... \n", "YOLOv5s-seg summary: 224 layers, 7611485 parameters, 0 gradients, 26.4 GFLOPs\n", "\u001b[34m\u001b[1mval: \u001b[0mScanning /content/datasets/coco/val2017... 4952 images, 48 backgrounds, 0 corrupt: 100% 5000/5000 [00:03<00:00, 1361.31it/s]\n", "\u001b[34m\u001b[1mval: \u001b[0mNew cache created: /content/datasets/coco/val2017.cache\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P          R      mAP50  mAP50-95): 100% 157/157 [01:54<00:00,  1.37it/s]\n", "                   all       5000      36335      0.673      0.517      0.566      0.373      0.672       0.49      0.532      0.319\n", "Speed: 0.6ms pre-process, 4.4ms inference, 2.9ms NMS per image at shape (32, 3, 640, 640)\n", "Results saved to \u001b[1mruns/val-seg/exp\u001b[0m\n"]}], "source": ["# Validate YOLOv5s-seg on COCO val\n", "!python segment/val.py --weights yolov5s-seg.pt --data coco.yaml --img 640 --half"]}, {"cell_type": "markdown", "metadata": {"id": "ZY2VXXXu74w5"}, "source": ["# 3. <PERSON>\n", "\n", "<p align=\"\"><a href=\"https://roboflow.com/?ref=ultralytics\"><img width=\"1000\" src=\"https://github.com/ultralytics/assets/raw/main/im/integrations-loop.png\"/></a></p>\n", "Close the active learning loop by sampling images from your inference conditions with the `roboflow` pip package\n", "<br><br>\n", "\n", "Train a YOLOv5s-seg model on the [COCO128](https://www.kaggle.com/ultralytics/coco128) dataset with `--data coco128-seg.yaml`, starting from pretrained `--weights yolov5s-seg.pt`, or from randomly initialized `--weights '' --cfg yolov5s-seg.yaml`.\n", "\n", "- **Pretrained [Models](https://github.com/ultralytics/yolov5/tree/master/models)** are downloaded\n", "automatically from the [latest YOLOv5 release](https://github.com/ultralytics/yolov5/releases)\n", "- **[Datasets](https://github.com/ultralytics/yolov5/tree/master/data)** available for autodownload include: [COCO](https://github.com/ultralytics/yolov5/blob/master/data/coco.yaml), [COCO128](https://github.com/ultralytics/yolov5/blob/master/data/coco128.yaml), [VOC](https://github.com/ultralytics/yolov5/blob/master/data/VOC.yaml), [Argoverse](https://github.com/ultralytics/yolov5/blob/master/data/Argoverse.yaml), [VisDrone](https://github.com/ultralytics/yolov5/blob/master/data/VisDrone.yaml), [GlobalWheat](https://github.com/ultralytics/yolov5/blob/master/data/GlobalWheat2020.yaml), [xView](https://github.com/ultralytics/yolov5/blob/master/data/xView.yaml), [Objects365](https://github.com/ultralytics/yolov5/blob/master/data/Objects365.yaml), [SKU-110K](https://github.com/ultralytics/yolov5/blob/master/data/SKU-110K.yaml).\n", "- **Training Results** are saved to `runs/train-seg/` with incrementing run directories, i.e. `runs/train-seg/exp2`, `runs/train-seg/exp3` etc.\n", "<br><br>\n", "\n", "A **Mosaic Dataloader** is used for training which combines 4 images into 1 mosaic.\n", "\n", "## Train on Custom Data with Rob<PERSON>low 🌟 NEW\n", "\n", "[Robof<PERSON>](https://roboflow.com/?ref=ultralytics) enables you to easily **organize, label, and prepare** a high quality dataset with your own custom data. Roboflow also makes it easy to establish an active learning pipeline, collaborate with your team on dataset improvement, and integrate directly into your model building workflow with the `roboflow` pip package.\n", "\n", "- Custom Training Example: [https://blog.roboflow.com/train-yolov5-instance-segmentation-custom-dataset/](https://blog.roboflow.com/train-yolov5-instance-segmentation-custom-dataset/?ref=ultralytics)\n", "- Custom Training Notebook: [![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/drive/1JTz7kpmHsg-5qwVz2d2IH3AaenI1tv0N?usp=sharing)\n", "<br>\n", "\n", "<p align=\"\"><a href=\"https://roboflow.com/?ref=ultralytics\"><img width=\"480\" src=\"https://robflow-public-assets.s3.amazonaws.com/how-to-train-yolov5-segmentation-annotation.gif\"/></a></p>Label images lightning fast (including with model-assisted labeling)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "i3oKtE4g-aNn"}, "outputs": [], "source": ["#@title Select YOLOv5 🚀 logger {run: 'auto'}\n", "logger = 'TensorBoard' #@param ['TensorBoard', 'Comet', 'ClearML']\n", "\n", "if logger == 'TensorBoard':\n", "  %load_ext tensorboard\n", "  %tensorboard --logdir runs/train-seg\n", "elif logger == 'Comet':\n", "  %pip install -q comet_ml\n", "  import comet_ml; comet_ml.init()\n", "elif logger == 'ClearML':\n", "  import clearml; clearml.browser_login()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "1NcFxRcFdJ_O", "outputId": "3a3e0cf7-e79c-47a5-c8e7-2d26eeeab988"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[34m\u001b[1msegment/train: \u001b[0mweights=yolov5s-seg.pt, cfg=, data=coco128-seg.yaml, hyp=data/hyps/hyp.scratch-low.yaml, epochs=3, batch_size=16, imgsz=640, rect=False, resume=False, nosave=False, noval=False, noautoanchor=False, noplots=False, evolve=None, bucket=, cache=ram, image_weights=False, device=, multi_scale=False, single_cls=False, optimizer=SGD, sync_bn=False, workers=8, project=runs/train-seg, name=exp, exist_ok=False, quad=False, cos_lr=False, label_smoothing=0.0, patience=100, freeze=[0], save_period=-1, seed=0, local_rank=-1, mask_ratio=4, no_overlap=False\n", "\u001b[34m\u001b[1mgithub: \u001b[0mup to date with https://github.com/ultralytics/yolov5 ✅\n", "YOLOv5 🚀 v7.0-2-gc9d47ae Python-3.7.15 torch-1.12.1+cu113 CUDA:0 (Tesla T4, 15110MiB)\n", "\n", "\u001b[34m\u001b[1mhyperparameters: \u001b[0mlr0=0.01, lrf=0.01, momentum=0.937, weight_decay=0.0005, warmup_epochs=3.0, warmup_momentum=0.8, warmup_bias_lr=0.1, box=0.05, cls=0.5, cls_pw=1.0, obj=1.0, obj_pw=1.0, iou_t=0.2, anchor_t=4.0, fl_gamma=0.0, hsv_h=0.015, hsv_s=0.7, hsv_v=0.4, degrees=0.0, translate=0.1, scale=0.5, shear=0.0, perspective=0.0, flipud=0.0, fliplr=0.5, mosaic=1.0, mixup=0.0, copy_paste=0.0\n", "\u001b[34m\u001b[1mTensorBoard: \u001b[0mStart with 'tensorboard --logdir runs/train-seg', view at http://localhost:6006/\n", "\n", "Dataset not found ⚠️, missing paths ['/content/datasets/coco128-seg/images/train2017']\n", "Downloading https://ultralytics.com/assets/coco128-seg.zip to coco128-seg.zip...\n", "100% 6.79M/6.79M [00:01<00:00, 6.73MB/s]\n", "Dataset download success ✅ (1.9s), saved to \u001b[1m/content/datasets\u001b[0m\n", "\n", "                 from  n    params  module                                  arguments                     \n", "  0                -1  1      3520  models.common.Conv                      [3, 32, 6, 2, 2]              \n", "  1                -1  1     18560  models.common.Conv                      [32, 64, 3, 2]                \n", "  2                -1  1     18816  models.common.C3                        [64, 64, 1]                   \n", "  3                -1  1     73984  models.common.Conv                      [64, 128, 3, 2]               \n", "  4                -1  2    115712  models.common.C3                        [128, 128, 2]                 \n", "  5                -1  1    295424  models.common.Conv                      [128, 256, 3, 2]              \n", "  6                -1  3    625152  models.common.C3                        [256, 256, 3]                 \n", "  7                -1  1   1180672  models.common.Conv                      [256, 512, 3, 2]              \n", "  8                -1  1   1182720  models.common.C3                        [512, 512, 1]                 \n", "  9                -1  1    656896  models.common.SPPF                      [512, 512, 5]                 \n", " 10                -1  1    131584  models.common.Conv                      [512, 256, 1, 1]              \n", " 11                -1  1         0  torch.nn.modules.upsampling.Upsample    [None, 2, 'nearest']          \n", " 12           [-1, 6]  1         0  models.common.Concat                    [1]                           \n", " 13                -1  1    361984  models.common.C3                        [512, 256, 1, False]          \n", " 14                -1  1     33024  models.common.Conv                      [256, 128, 1, 1]              \n", " 15                -1  1         0  torch.nn.modules.upsampling.Upsample    [None, 2, 'nearest']          \n", " 16           [-1, 4]  1         0  models.common.Concat                    [1]                           \n", " 17                -1  1     90880  models.common.C3                        [256, 128, 1, False]          \n", " 18                -1  1    147712  models.common.Conv                      [128, 128, 3, 2]              \n", " 19          [-1, 14]  1         0  models.common.Concat                    [1]                           \n", " 20                -1  1    296448  models.common.C3                        [256, 256, 1, False]          \n", " 21                -1  1    590336  models.common.Conv                      [256, 256, 3, 2]              \n", " 22          [-1, 10]  1         0  models.common.Concat                    [1]                           \n", " 23                -1  1   1182720  models.common.C3                        [512, 512, 1, False]          \n", " 24      [17, 20, 23]  1    615133  models.yolo.Segment                     [80, [[10, 13, 16, 30, 33, 23], [30, 61, 62, 45, 59, 119], [116, 90, 156, 198, 373, 326]], 32, 128, [128, 256, 512]]\n", "Model summary: 225 layers, 7621277 parameters, 7621277 gradients, 26.6 GFLOPs\n", "\n", "Transferred 367/367 items from yolov5s-seg.pt\n", "\u001b[34m\u001b[1mAMP: \u001b[0mchecks passed ✅\n", "\u001b[34m\u001b[1moptimizer:\u001b[0m SGD(lr=0.01) with parameter groups 60 weight(decay=0.0), 63 weight(decay=0.0005), 63 bias\n", "\u001b[34m\u001b[1malbumentations: \u001b[0mBlur(p=0.01, blur_limit=(3, 7)), MedianBlur(p=0.01, blur_limit=(3, 7)), ToGray(p=0.01), CLAHE(p=0.01, clip_limit=(1, 4.0), tile_grid_size=(8, 8))\n", "\u001b[34m\u001b[1mtrain: \u001b[0mScanning /content/datasets/coco128-seg/labels/train2017... 126 images, 2 backgrounds, 0 corrupt: 100% 128/128 [00:00<00:00, 1389.59it/s]\n", "\u001b[34m\u001b[1mtrain: \u001b[0mNew cache created: /content/datasets/coco128-seg/labels/train2017.cache\n", "\u001b[34m\u001b[1mtrain: \u001b[0mCaching images (0.1GB ram): 100% 128/128 [00:00<00:00, 238.86it/s]\n", "\u001b[34m\u001b[1mval: \u001b[0mScanning /content/datasets/coco128-seg/labels/train2017.cache... 126 images, 2 backgrounds, 0 corrupt: 100% 128/128 [00:00<?, ?it/s]\n", "\u001b[34m\u001b[1mval: \u001b[0mCaching images (0.1GB ram): 100% 128/128 [00:01<00:00, 98.90it/s]\n", "\n", "\u001b[34m\u001b[1mAutoAnchor: \u001b[0m4.27 anchors/target, 0.994 Best Possible Recall (BPR). Current anchors are a good fit to dataset ✅\n", "Plotting labels to runs/train-seg/exp/labels.jpg... \n", "Image sizes 640 train, 640 val\n", "Using 2 dataloader workers\n", "Logging results to \u001b[1mruns/train-seg/exp\u001b[0m\n", "Starting training for 3 epochs...\n", "\n", "      Epoch    GPU_mem   box_loss   seg_loss   obj_loss   cls_loss  Instances       Size\n", "        0/2      4.92G     0.0417    0.04646    0.06066    0.02126        192        640: 100% 8/8 [00:08<00:00,  1.10s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P          R      mAP50  mAP50-95): 100% 4/4 [00:02<00:00,  1.81it/s]\n", "                   all        128        929      0.737      0.649      0.715      0.492      0.719      0.617      0.658      0.408\n", "\n", "      Epoch    GPU_mem   box_loss   seg_loss   obj_loss   cls_loss  Instances       Size\n", "        1/2      6.29G    0.04157    0.04503    0.05772    0.01777        208        640: 100% 8/8 [00:09<00:00,  1.21s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P          R      mAP50  mAP50-95): 100% 4/4 [00:02<00:00,  1.87it/s]\n", "                   all        128        929      0.756      0.674      0.738      0.506      0.725       0.64       0.68      0.422\n", "\n", "      Epoch    GPU_mem   box_loss   seg_loss   obj_loss   cls_loss  Instances       Size\n", "        2/2      6.29G     0.0425    0.04793    0.06784    0.01863        161        640: 100% 8/8 [00:03<00:00,  2.02it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P          R      mAP50  mAP50-95): 100% 4/4 [00:02<00:00,  1.88it/s]\n", "                   all        128        929      0.736      0.694      0.747      0.522      0.769      0.622      0.683      0.427\n", "\n", "3 epochs completed in 0.009 hours.\n", "Optimizer stripped from runs/train-seg/exp/weights/last.pt, 15.6MB\n", "Optimizer stripped from runs/train-seg/exp/weights/best.pt, 15.6MB\n", "\n", "Validating runs/train-seg/exp/weights/best.pt...\n", "Fusing layers... \n", "Model summary: 165 layers, 7611485 parameters, 0 gradients, 26.4 GFLOPs\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P          R      mAP50  mAP50-95): 100% 4/4 [00:06<00:00,  1.59s/it]\n", "                   all        128        929      0.738      0.694      0.746      0.522      0.759      0.625      0.682      0.426\n", "                person        128        254      0.845      0.756      0.836       0.55      0.861      0.669      0.759      0.407\n", "               bicycle        128          6      0.475      0.333      0.549      0.341      0.711      0.333      0.526      0.322\n", "                   car        128         46      0.612      0.565      0.539      0.257      0.555      0.435      0.477      0.171\n", "            motorcycle        128          5       0.73        0.8      0.752      0.571      0.747        0.8      0.752       0.42\n", "              airplane        128          6          1      0.943      0.995      0.732       0.92      0.833      0.839      0.555\n", "                   bus        128          7      0.677      0.714      0.722      0.653      0.711      0.714      0.722      0.593\n", "                 train        128          3          1      0.951      0.995      0.551          1      0.884      0.995      0.781\n", "                 truck        128         12      0.555      0.417      0.457      0.285      0.624      0.417      0.397      0.277\n", "                  boat        128          6      0.624        0.5      0.584      0.186          1      0.326      0.412      0.133\n", "         traffic light        128         14      0.513      0.302      0.411      0.247      0.435      0.214      0.376      0.251\n", "             stop sign        128          2      0.824          1      0.995      0.796      0.906          1      0.995      0.747\n", "                 bench        128          9       0.75      0.667      0.763      0.367      0.724      0.585      0.698      0.209\n", "                  bird        128         16      0.961          1      0.995      0.686      0.918      0.938       0.91      0.525\n", "                   cat        128          4      0.771      0.857      0.945      0.752       0.76        0.8      0.945      0.728\n", "                   dog        128          9      0.987      0.778      0.963      0.681          1      0.705       0.89      0.574\n", "                 horse        128          2      0.703          1      0.995      0.697      0.759          1      0.995      0.249\n", "              elephant        128         17      0.916      0.882       0.93      0.691      0.811      0.765      0.829      0.537\n", "                  bear        128          1      0.664          1      0.995      0.995      0.701          1      0.995      0.895\n", "                 zebra        128          4      0.864          1      0.995      0.921      0.879          1      0.995      0.804\n", "               giraffe        128          9      0.883      0.889       0.94      0.683      0.845      0.778       0.78      0.463\n", "              backpack        128          6          1       0.59      0.701      0.372          1      0.474       0.52      0.252\n", "              umbrella        128         18      0.654      0.839      0.887       0.52      0.517      0.556      0.427      0.229\n", "               handbag        128         19       0.54      0.211      0.408      0.221      0.796      0.206      0.396      0.196\n", "                   tie        128          7      0.864      0.857      0.857      0.577      0.925      0.857      0.857      0.534\n", "              suitcase        128          4      0.716          1      0.945      0.647      0.767          1      0.945      0.634\n", "               frisbee        128          5      0.708        0.8      0.761      0.643      0.737        0.8      0.761      0.501\n", "                  skis        128          1      0.691          1      0.995      0.796      0.761          1      0.995      0.199\n", "             snowboard        128          7      0.918      0.857      0.904      0.604       0.32      0.286      0.235      0.137\n", "           sports ball        128          6      0.902      0.667      0.701      0.466      0.727        0.5      0.497      0.471\n", "                  kite        128         10      0.586        0.4      0.511      0.231      0.663      0.394      0.417      0.139\n", "          baseball bat        128          4      0.359        0.5      0.401      0.169      0.631        0.5      0.526      0.133\n", "        baseball glove        128          7          1      0.519       0.58      0.327      0.687      0.286      0.455      0.328\n", "            skateboard        128          5      0.729        0.8      0.862      0.631      0.599        0.6      0.604      0.379\n", "         tennis racket        128          7       0.57      0.714      0.645      0.448      0.608      0.714      0.645      0.412\n", "                bottle        128         18      0.469      0.393      0.537      0.357      0.661      0.389      0.543      0.349\n", "            wine glass        128         16      0.677      0.938      0.866      0.441       0.53      0.625       0.67      0.334\n", "                   cup        128         36      0.777      0.722      0.812      0.466      0.725      0.583      0.762      0.467\n", "                  fork        128          6      0.948      0.333      0.425       0.27      0.527      0.167       0.18      0.102\n", "                 knife        128         16      0.757      0.587      0.669      0.458       0.79        0.5      0.552       0.34\n", "                 spoon        128         22       0.74      0.364      0.559      0.269      0.925      0.364      0.513      0.213\n", "                  bowl        128         28      0.766      0.714      0.725      0.559      0.803      0.584      0.665      0.353\n", "                banana        128          1      0.408          1      0.995      0.398      0.539          1      0.995      0.497\n", "              sandwich        128          2          1          0      0.695      0.536          1          0      0.498      0.448\n", "                orange        128          4      0.467          1      0.995      0.693      0.518          1      0.995      0.663\n", "              broccoli        128         11      0.462      0.455      0.383      0.259      0.548      0.455      0.384      0.256\n", "                carrot        128         24      0.631      0.875       0.77      0.533      0.757      0.909      0.853      0.499\n", "               hot dog        128          2      0.555          1      0.995      0.995      0.578          1      0.995      0.796\n", "                 pizza        128          5       0.89        0.8      0.962      0.796          1      0.778      0.962      0.766\n", "                 donut        128         14      0.695          1      0.893      0.772      0.704          1      0.893      0.696\n", "                  cake        128          4      0.826          1      0.995       0.92      0.862          1      0.995      0.846\n", "                 chair        128         35       0.53      0.571      0.613      0.336       0.67        0.6      0.538      0.271\n", "                 couch        128          6      0.972      0.667      0.833      0.627          1       0.62      0.696      0.394\n", "          potted plant        128         14        0.7      0.857      0.883      0.552      0.836      0.857      0.883      0.473\n", "                   bed        128          3      0.979      0.667       0.83      0.366          1          0       0.83      0.373\n", "          dining table        128         13      0.775      0.308      0.505      0.364      0.644      0.231       0.25     0.0804\n", "                toilet        128          2      0.836          1      0.995      0.846      0.887          1      0.995      0.797\n", "                    tv        128          2        0.6          1      0.995      0.846      0.655          1      0.995      0.896\n", "                laptop        128          3      0.822      0.333      0.445      0.307          1          0      0.392       0.12\n", "                 mouse        128          2          1          0          0          0          1          0          0          0\n", "                remote        128          8      0.745        0.5       0.62      0.459      0.821        0.5      0.624      0.449\n", "            cell phone        128          8      0.686      0.375      0.502      0.272      0.488       0.25       0.28      0.132\n", "             microwave        128          3      0.831          1      0.995      0.722      0.867          1      0.995      0.592\n", "                  oven        128          5      0.439        0.4      0.435      0.294      0.823        0.6      0.645      0.418\n", "                  sink        128          6      0.677        0.5      0.565      0.448      0.722        0.5       0.46      0.362\n", "          refrigerator        128          5      0.533        0.8      0.783      0.524      0.558        0.8      0.783      0.527\n", "                  book        128         29      0.732      0.379      0.423      0.196       0.69      0.207       0.38      0.131\n", "                 clock        128          9      0.889      0.778      0.917      0.677      0.908      0.778      0.875      0.604\n", "                  vase        128          2      0.375          1      0.995      0.995      0.455          1      0.995      0.796\n", "              scissors        128          1          1          0     0.0166    0.00166          1          0          0          0\n", "            teddy bear        128         21      0.813      0.829      0.841      0.457      0.826      0.678      0.786      0.422\n", "            toothbrush        128          5      0.806          1      0.995      0.733      0.991          1      0.995      0.628\n", "Results saved to \u001b[1mruns/train-seg/exp\u001b[0m\n"]}], "source": ["# Train YOLOv5s on COCO128 for 3 epochs\n", "!python segment/train.py --img 640 --batch 16 --epochs 3 --data coco128-seg.yaml --weights yolov5s-seg.pt --cache"]}, {"cell_type": "markdown", "metadata": {"id": "15glLzbQx5u0"}, "source": ["# 4. Visualize"]}, {"cell_type": "markdown", "metadata": {"id": "nWOsI5wJR1o3"}, "source": ["## Comet Logging and Visualization 🌟 NEW\n", "\n", "[Comet](https://www.comet.com/site/lp/yolov5-with-comet/?utm_source=yolov5&utm_medium=partner&utm_campaign=partner_yolov5_2022&utm_content=yolov5_colab) is now fully integrated with YOLOv5. Track and visualize model metrics in real time, save your hyperparameters, datasets, and model checkpoints, and visualize your model predictions with [Comet Custom Panels](https://www.comet.com/docs/v2/guides/comet-dashboard/code-panels/about-panels/?utm_source=yolov5&utm_medium=partner&utm_campaign=partner_yolov5_2022&utm_content=yolov5_colab)! Comet makes sure you never lose track of your work and makes it easy to share results and collaborate across teams of all sizes!\n", "\n", "Getting started is easy:\n", "```shell\n", "pip install comet_ml  # 1. install\n", "export COMET_API_KEY=<Your API Key>  # 2. paste API key\n", "python train.py --img 640 --epochs 3 --data coco128.yaml --weights yolov5s.pt  # 3. train\n", "```\n", "To learn more about all of the supported Comet features for this integration, check out the [Comet Tutorial](https://github.com/ultralytics/yolov5/tree/master/utils/loggers/comet). If you'd like to learn more about Comet, head over to our [documentation](https://www.comet.com/docs/v2/?utm_source=yolov5&utm_medium=partner&utm_campaign=partner_yolov5_2022&utm_content=yolov5_colab). Get started by trying out the Comet Colab Notebook:\n", "[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/drive/1RG0WOQyxlDlo5Km8GogJpIEJlg_5lyYO?usp=sharing)\n", "\n", "<a href=\"https://bit.ly/yolov5-readme-comet2\">\n", "<img alt=\"Comet Dashboard\" src=\"https://user-images.githubusercontent.com/26833433/202851203-164e94e1-2238-46dd-91f8-de020e9d6b41.png\" width=\"1280\"/></a>"]}, {"cell_type": "markdown", "metadata": {"id": "Lay2WsTjNJzP"}, "source": ["## ClearML Logging and Automation 🌟 NEW\n", "\n", "[ClearML](https://cutt.ly/yolov5-notebook-clearml) is completely integrated into YOLOv5 to track your experimentation, manage dataset versions and even remotely execute training runs. To enable ClearML (check cells above):\n", "\n", "- `pip install clearml`\n", "- run `clearml-init` to connect to a ClearML server (**deploy your own [open-source server](https://github.com/allegroai/clearml-server)**, or use our [free hosted server](https://cutt.ly/yolov5-notebook-clearml))\n", "\n", "You'll get all the great expected features from an experiment manager: live updates, model upload, experiment comparison etc. but ClearML also tracks uncommitted changes and installed packages for example. Thanks to that ClearML Tasks (which is what we call experiments) are also reproducible on different machines! With only 1 extra line, we can schedule a YOLOv5 training task on a queue to be executed by any number of ClearML Agents (workers).\n", "\n", "You can use ClearML Data to version your dataset and then pass it to YOLOv5 simply using its unique ID. This will help you keep track of your data without adding extra hassle. Explore the [ClearML Tutorial](https://github.com/ultralytics/yolov5/tree/master/utils/loggers/clearml) for details!\n", "\n", "<a href=\"https://cutt.ly/yolov5-notebook-clearml\">\n", "<img alt=\"ClearML Experiment Management UI\" src=\"https://github.com/thepycoder/clearml_screenshots/raw/main/scalars.jpg\" width=\"1280\"/></a>"]}, {"cell_type": "markdown", "metadata": {"id": "-WPvRbS5Swl6"}, "source": ["## Local Logging\n", "\n", "Training results are automatically logged with [Tensorboard](https://www.tensorflow.org/tensorboard) and [CSV](https://github.com/ultralytics/yolov5/pull/4148) loggers to `runs/train`, with a new experiment directory created for each new training as `runs/train/exp2`, `runs/train/exp3`, etc.\n", "\n", "This directory contains train and val statistics, mosaics, labels, predictions and augmentated mosaics, as well as metrics and charts including precision-recall (PR) curves and confusion matrices. \n", "\n", "<img alt=\"Local logging results\" src=\"https://user-images.githubusercontent.com/26833433/183222430-e1abd1b7-782c-4cde-b04d-ad52926bf818.jpg\" width=\"1280\"/>\n"]}, {"cell_type": "markdown", "metadata": {"id": "Zelyeqbyt3GD"}, "source": ["# Environments\n", "\n", "YOLOv5 may be run in any of the following up-to-date verified environments (with all dependencies including [CUDA](https://developer.nvidia.com/cuda)/[CUDNN](https://developer.nvidia.com/cudnn), [Python](https://www.python.org/) and [PyTorch](https://pytorch.org/) preinstalled):\n", "\n", "- **Notebooks** with free GPU: <a href=\"https://bit.ly/yolov5-paperspace-notebook\"><img src=\"https://assets.paperspace.io/img/gradient-badge.svg\" alt=\"Run on Gradient\"></a> <a href=\"https://colab.research.google.com/github/ultralytics/yolov5/blob/master/tutorial.ipynb\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"></a> <a href=\"https://www.kaggle.com/ultralytics/yolov5\"><img src=\"https://kaggle.com/static/images/open-in-kaggle.svg\" alt=\"Open In Kaggle\"></a>\n", "- **Google Cloud** Deep Learning VM. See [GCP Quickstart Guide](https://github.com/ultralytics/yolov5/wiki/GCP-Quickstart)\n", "- **Amazon** Deep Learning AMI. See [AWS Quickstart Guide](https://github.com/ultralytics/yolov5/wiki/AWS-Quickstart)\n", "- **Docker Image**. See [Docker Quickstart Guide](https://github.com/ultralytics/yolov5/wiki/Docker-Quickstart) <a href=\"https://hub.docker.com/r/ultralytics/yolov5\"><img src=\"https://img.shields.io/docker/pulls/ultralytics/yolov5?logo=docker\" alt=\"Docker Pulls\"></a>\n"]}, {"cell_type": "markdown", "metadata": {"id": "6Qu7Iesl0p54"}, "source": ["# Status\n", "\n", "![YOLOv5 CI](https://github.com/ultralytics/yolov5/actions/workflows/ci-testing.yml/badge.svg)\n", "\n", "If this badge is green, all [YOLOv5 GitHub Actions](https://github.com/ultralytics/yolov5/actions) Continuous Integration (CI) tests are currently passing. CI tests verify correct operation of YOLOv5 training ([train.py](https://github.com/ultralytics/yolov5/blob/master/train.py)), testing ([val.py](https://github.com/ultralytics/yolov5/blob/master/val.py)), inference ([detect.py](https://github.com/ultralytics/yolov5/blob/master/detect.py)) and export ([export.py](https://github.com/ultralytics/yolov5/blob/master/export.py)) on macOS, Windows, and Ubuntu every 24 hours and on every commit.\n"]}, {"cell_type": "markdown", "metadata": {"id": "IEijrePND_2I"}, "source": ["# Appendix\n", "\n", "Additional content below."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "GMusP4OAxFu6"}, "outputs": [], "source": ["# YOLOv5 PyTorch HUB Inference (DetectionModels only)\n", "import torch\n", "\n", "model = torch.hub.load('ultralytics/yolov5', 'yolov5s-seg')  # yolov5n - yolov5x6 or custom\n", "im = 'https://ultralytics.com/images/zidane.jpg'  # file, Path, PIL.Image, OpenCV, nparray, list\n", "results = model(im)  # inference\n", "results.print()  # or .show(), .save(), .crop(), .pandas(), etc."]}], "metadata": {"accelerator": "GPU", "colab": {"name": "YOLOv5 Segmentation Tutorial", "provenance": [], "toc_visible": true}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.12"}}, "nbformat": 4, "nbformat_minor": 0}