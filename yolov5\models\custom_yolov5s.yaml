anchors:
- - 10
  - 13
  - 16
  - 30
  - 33
  - 23
- - 30
  - 61
  - 62
  - 45
  - 59
  - 119
- - 116
  - 90
  - 156
  - 198
  - 373
  - 326
backbone:
- - -1
  - 1
  - Conv
  - - 64
    - 6
    - 2
    - 2
- - -1
  - 1
  - Conv
  - - 128
    - 3
    - 2
- - -1
  - 3
  - C3
  - - 128
- - -1
  - 1
  - Conv
  - - 256
    - 3
    - 2
- - -1
  - 6
  - C3
  - - 256
- - -1
  - 1
  - Conv
  - - 512
    - 3
    - 2
- - -1
  - 9
  - C3
  - - 512
- - -1
  - 1
  - Conv
  - - 1024
    - 3
    - 2
- - -1
  - 3
  - C3
  - - 1024
- - -1
  - 1
  - SPPF
  - - 1024
    - 5
depth_multiple: 0.33
head:
- - -1
  - 1
  - Conv
  - - 512
    - 1
    - 1
- - -1
  - 1
  - nn.Upsample
  - - None
    - 2
    - nearest
- - - -1
    - 6
  - 1
  - Concat
  - - 1
- - -1
  - 3
  - C3
  - - 512
    - false
- - -1
  - 1
  - Conv
  - - 256
    - 1
    - 1
- - -1
  - 1
  - nn.Upsample
  - - None
    - 2
    - nearest
- - - -1
    - 4
  - 1
  - Concat
  - - 1
- - -1
  - 3
  - C3
  - - 256
    - false
- - -1
  - 1
  - Conv
  - - 256
    - 3
    - 2
- - - -1
    - 14
  - 1
  - Concat
  - - 1
- - -1
  - 3
  - C3
  - - 512
    - false
- - -1
  - 1
  - Conv
  - - 512
    - 3
    - 2
- - - -1
    - 10
  - 1
  - Concat
  - - 1
- - -1
  - 3
  - C3
  - - 1024
    - false
- - - 17
    - 20
    - 23
  - 1
  - Detect
  - - nc
    - anchors
nc: 6
width_multiple: 0.5
